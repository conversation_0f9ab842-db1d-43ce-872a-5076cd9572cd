<script setup lang="ts">
import Vditor from 'vditor';
import 'vditor/dist/index.css';
import { ref, onMounted, defineProps, defineExpose, defineModel, watch, withDefaults } from 'vue';
import { ElMessage } from 'element-plus';
import { http } from '@/apis';
import {
  safeFilename,
  isFileSizeExceeded,
  revertMathScriptsToMd,
  unescapeLatexFormulas
} from './utils';
import { convertLanguageMathToScript } from '@/utils/latexUtils';
interface VeditorInlineProps {
  disabled?: boolean;
  height?: number;
  mode?: 'ir' | 'wysiwyg' | 'sv';
  placeholder?: string;
  showToolbar?: boolean; // 控制工具栏是否显示
  autoHeight?: boolean; // 是否自动调整高度
  maxHeight?: number; // 最大高度限制
  counterMax?: number; // 字符计数器最大值限制
}

const props = withDefaults(defineProps<VeditorInlineProps>(), {
  disabled: false,
  height: 150, // inline版本默认高度更小
  mode: 'ir', //
  placeholder: '请输入',
  showToolbar: false, // 默认不显示工具栏
  autoHeight: false, // 默认不自动调整高度
  maxHeight: 100 // 默认最大高度100px
  // counterMax 不设置默认值，只有明确传入时才启用计数器
});

const vditor = ref<Vditor>();
const model = defineModel<string>();
const isReady = ref(false);

// 添加唯一ID生成
const editorId = ref(`vditor-${Math.random().toString(36).substring(2, 11)}`);

// 优化的滚动条控制 - 防止闪烁的最佳方案
const checkContentOverflow = () => {
  if (!props.autoHeight || !vditor.value) return;

  const editor = document.getElementById(editorId.value);
  const contentElement = editor?.querySelector('.vditor-content') as HTMLElement;

  if (contentElement) {
    // 使用双重延迟确保DOM完全更新
    requestAnimationFrame(() => {
      setTimeout(() => {
        const scrollHeight = contentElement.scrollHeight;
        const maxHeight = props.maxHeight;

        if (scrollHeight > maxHeight) {
          contentElement.classList.add('content-overflow');
        } else {
          contentElement.classList.remove('content-overflow');
        }
      }, 10); // 额外10ms延迟确保稳定
    });
  }
};

watch(
  () => model.value,
  (newVal) => {
    if (vditor.value && isReady.value && newVal !== vditor.value.getValue()) {
      vditor.value.setValue(newVal || '');
      // 内容变化后检查滚动条状态
      if (props.autoHeight) {
        checkContentOverflow();
      }
    }
  },
  { deep: true }
);

onMounted(() => {
  if (model.value) {
    model.value += ' ';
  }

  vditor.value = new Vditor(editorId.value, {
    // 使用动态生成的ID
    height: props.autoHeight ? 'auto' : props.height,
    width: '100%',
    mode: props.mode,
    cache: {
      enable: false
    },
    minHeight: props.autoHeight ? props.height : undefined,
    resize: {
      enable: false
    },
    preview: {
      math: {
        engine: 'KaTeX'
      },
      markdown: {
        sanitize: true,
        autoSpace: true,
        paragraphBeginningSpace: true
      },
      hljs: {
        style: 'agate'
      }
    },
    cdn: '../../../vditor',
    toolbar: props.showToolbar
      ? [
          'headings',
          'bold',
          'italic',
          'strike',
          'link',
          '|',
          'list',
          'ordered-list',
          'check',
          'outdent',
          'indent',
          '|',
          'quote',
          'line',
          'code',
          'inline-code',
          'table',
          'edit-mode',
          'both'
          // "|",
          // 'upload'

          // "|",
          // "undo",
          // "redo",
          // "|",

          // "preview",
          // "outline",
          // "code-theme",
          // "export",
        ]
      : [],
    hint: {
      extend: [] // 禁用扩展提示
    },
    comment: {
      enable: false // 禁用评论功能
    },
    counter: {
      enable: props.counterMax !== undefined, // 只有传入 counterMax 参数时才启用计数器
      max: props.counterMax || 1800, // 如果启用但未设置值，使用默认值 1800
      type: 'markdown'
    },

    after: () => {
      isReady.value = true;
      if (model.value) {
        vditor.value?.setValue(model.value.trim());
      }

      // 初始化时检查内容溢出状态
      if (props.autoHeight) {
        // 延迟执行，确保Vditor完全初始化
        setTimeout(() => {
          checkContentOverflow();
        }, 150);
      }

      // 修改事件监听，使用动态ID，只有在显示工具栏时才添加事件监听
      if (props.showToolbar) {
        const editor = document.getElementById(editorId.value);
        if (editor) {
          editor.addEventListener('click', () => {
            editor.classList.add('toolbar-visible');
          });

          editor.addEventListener(
            'blur',
            (event) => {
              const toolbar = editor.querySelector('.vditor-toolbar');
              if (toolbar && !toolbar.contains(event.relatedTarget as Node)) {
                editor.classList.remove('toolbar-visible');
              }
            },
            true
          );
        }
      }
    },
    input: (value: string) => {
      model.value = value;
      // 内容变化时检查是否需要显示滚动条
      if (props.autoHeight) {
        checkContentOverflow();
      }
    },
    placeholder: props.placeholder
  });

  if (props.disabled) {
    vditor.value.disabled();
  }
});

const getData = (): string => {
  if (vditor.value) {
    return vditor.value.getValue();
  }
  return '';
};

const setData = (value: string, dis?: boolean) => {
  if (vditor.value && isReady.value) {
    vditor.value.setValue(value);
    if (dis !== undefined) {
      if (dis) {
        vditor.value.disabled();
      } else {
        vditor.value.enable();
      }
    }
  }
};
const getHtml = (): string => {
  if (vditor.value) {
    return convertLanguageMathToScript(vditor.value.getHTML());
  }
  return '';
};

const html2Md = (html: string): string => {
  if (vditor.value && isReady.value) {
    return unescapeLatexFormulas(revertMathScriptsToMd(vditor.value.html2md(html)));
  }
  return '';
};

// 延迟转换HTML为Markdown的方法
const html2MdWhenReady = (html: string): Promise<string> => {
  return new Promise((resolve) => {
    if (vditor.value && isReady.value) {
      // 如果已经就绪，立即转换
      // console.log('html', html);
      let result = vditor.value.html2md(revertMathScriptsToMd(html));
      // 处理LaTeX公式转义问题
      result = unescapeLatexFormulas(result);
      // console.log('result', result);
      resolve(result);
    } else {
      // 如果未就绪，等待就绪后转换
      const checkReady = () => {
        if (vditor.value && isReady.value) {
          // console.log('html', html);

          let result = vditor.value.html2md(revertMathScriptsToMd(html));
          // 处理LaTeX公式转义问题
          result = unescapeLatexFormulas(result);
          // console.log('result', result);

          resolve(result);
        } else {
          setTimeout(checkReady, 100);
        }
      };
      checkReady();
    }
  });
};
defineExpose({
  getData,
  setData,
  getHtml,
  html2MdWhenReady
});
</script>

<template>
  <!-- 使用动态生成的ID -->
  <div :id="editorId" class="vditor-inline-container" :data-auto-height="props.autoHeight"></div>
</template>

<style scoped>
.vditor-inline-container {
  border: 1px solid rgb(220, 223, 230);
  border-radius: 2px;
  position: relative;
}

/* 自适应高度时的样式 - 优化方案 */
.vditor-inline-container[data-auto-height='true'] :deep(.vditor-content) {
  margin-top: 0;
  padding: 0 !important;
  min-height: v-bind('props.height + "px"') !important;
  max-height: v-bind('props.maxHeight + "px"') !important;
  height: auto !important;
  overflow-x: hidden;
  /* 默认隐藏滚动条，防止闪烁 */
  overflow-y: hidden;
  transition: all 0.15s ease;
}

/* 当内容确实超出时才显示滚动条 */
.vditor-inline-container[data-auto-height='true'] :deep(.vditor-content.content-overflow) {
  overflow-y: auto;
}

/* 固定高度时的样式 */
.vditor-inline-container[data-auto-height='false'] :deep(.vditor-content) {
  margin-top: 0;
  padding: 0 !important;
  min-height: unset !important;
  height: v-bind('props.height + "px"') !important;
  overflow: hidden;
}

/* 重置内容区域样式 */
:deep(.vditor-reset) {
  padding: 5px 10px !important;
  width: 100% !important;
  p {
    margin-bottom: 0px;
  }
  /* 修复列表样式 */
  & ul {
    list-style-type: disc !important;
    padding-left: 20px !important;
  }

  & ol {
    list-style-type: decimal !important;
    padding-left: 20px !important;
  }

  /* 修复列表项标记 */
  & li::marker {
    content: initial !important;
    color: initial !important;
  }
}

/* 移除所有滚动条相关样式 */

/* 工具栏样式保持不变 */
:deep(.vditor-toolbar) {
  position: absolute;
  left: -1px;
  right: -1px;
  bottom: 100%;
  z-index: 1000;
  background-color: #fff;
  border: 1px solid #ccced1;
  border-bottom: none;
  border-radius: 5px 5px 0 0;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  margin-bottom: -1px;
}

.toolbar-visible :deep(.vditor-toolbar) {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.toolbar-visible {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

/* 添加以下样式来隐藏面板 */
:deep(.vditor-panel) {
  display: none !important;
}
</style>
